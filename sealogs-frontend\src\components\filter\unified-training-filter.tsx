'use client'

import { useEffect, useState, useCallback, memo } from 'react'
import VesselDropdown from './components/vessel-dropdown'
import TrainingTypeDropdown from './components/training-type-dropdown'
import CrewDropdown from './components/crew-dropdown/crew-dropdown'
import DateRange from '../DateRange'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import { useBreakpoints } from '../hooks/useBreakpoints'
import { Combobox } from '@/components/ui/comboBox'

// TypeScript interfaces for unified training filter
export interface UnifiedTrainingFilterProps {
    onChange: (filterData: { type: string; data: any }) => void
    vesselIdOptions?: any[]
    trainingTypeIdOptions?: any[]
    memberId?: number
    trainerIdOptions?: any[]
    memberIdOptions?: any[]
}

export interface UnifiedTrainingFilterData {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

const UnifiedTrainingFilterComponent = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: UnifiedTrainingFilterProps) => {
    // Category options for the combobox
    const categoryOptions = [
        {
            label: 'Status',
            value: 'all',
        },
        {
            label: 'Overdue',
            value: 'overdue',
        },
        {
            label: 'Upcoming',
            value: 'upcoming',
        },
        {
            label: 'Completed',
            value: 'completed',
        },
    ]

    const [selectedCategory, setSelectedCategory] = useState<any>(
        categoryOptions[0], // Default to "All Categories"
    )

    // Memoize the dropdown change handler to prevent unnecessary re-renders
    const handleDropdownChange = useCallback(
        (type: string, data: any) => {
            onChange({ type, data })
        },
        [onChange],
    )

    // Memoize the category change handler
    const handleCategoryChange = useCallback(
        (option: any) => {
            setSelectedCategory(option)
            handleDropdownChange('category', option?.value || 'all')
        },
        [handleDropdownChange],
    )

    const bp = useBreakpoints()

    // Responsive date format based on screen size
    const getResponsiveDateFormat = () => {
        if (bp.laptop) {
            // Large screens (desktop): Full format
            return 'MMM do, yyyy' // e.g., "Jan 1st, 2024"
        } else if (bp['tablet-sm']) {
            // Medium screens (tablet): Abbreviated format
            return 'MMM d, yyyy' // e.g., "Jan 1, 2024"
        } else {
            // Small screens (mobile): Compact format
            return 'M/d/yy' // e.g., "1/1/24"
        }
    }

    const filterContent = (
        <div className="grid tablet-sm:grid-cols-3 desktop:grid-cols-7 gap-2.5">
            <div className="     tablet-sm:col-span-1 desktop:col-span-2 ">
                <DateRange
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                    dateFormat={getResponsiveDateFormat()}
                    clearable
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <CrewDropdown
                    label=""
                    placeholder="Trainer"
                    isClearable={true}
                    multi
                    controlClasses="filter"
                    onChange={(data: any) => {
                        handleDropdownChange('trainer', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <CrewDropdown
                    isClearable={true}
                    label=""
                    multi
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) => {
                        handleDropdownChange('member', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    memberIdOptions={memberIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    placeholder="All Categories"
                    buttonClassName="w-full"
                    searchThreshold={10} // Disable search for small list
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
            </div>
        </div>
    )
    return (
        <>
            {bp['tablet-sm'] ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="unified-training-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}

// Export memoized component for better performance
export const UnifiedTrainingFilter = memo(UnifiedTrainingFilterComponent)
