'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { UnifiedTrainingTable } from './unified-training-table'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { Card } from '@/components/ui'
import { H2 } from '@/components/ui/typography'
import { mergeAndSortCrewTrainingData } from '@/app/ui/crew-training/utils/crew-training-utils'
import { UnifiedTrainingFilter } from '@/components/filter/unified-training-filter'
import { useUnifiedTrainingFilters } from './hooks/useUnifiedTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Loader2 } from 'lucide-react'

interface UnifiedTrainingExampleProps {
    vesselId?: number
    memberId?: number
    isVesselView?: boolean
}

export const UnifiedTrainingExample = ({
    vesselId = 0,
    memberId = 0,
    isVesselView = false,
}: UnifiedTrainingExampleProps) => {
    const [isLoading, setIsLoading] = useState(true)
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>(
        [],
    )
    const includeCompleted = true // Always include completed in unified view

    const [permissions, setPermissions] = useState<any>(false)

    const { getVesselWithIcon } = useVesselIconData()

    // Initialize permissions
    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // Query for training session dues (overdue/upcoming)
    const [queryTrainingSessionDues, { loading: duesLoading }] = useLazyQuery(
        READ_TRAINING_SESSION_DUES,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessionDues.nodes || []

                setTrainingSessionDues(data)
            },
            onError: (error: any) => {
                console.error('Error loading training session dues:', error)
            },
        },
    )

    // Query for completed training sessions
    const [queryCompletedTraining, { loading: completedLoading }] =
        useLazyQuery(TRAINING_SESSIONS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes || []

                setCompletedTrainingList(data)
            },
            onError: (error: any) => {
                console.error('Error loading completed training:', error)
            },
        })

    // Load training session dues function
    const loadTrainingSessionDues = useCallback(
        async (filter: any) => {
            const duesFilter: any = {}
            if (memberId && memberId > 0) {
                duesFilter.memberID = { eq: +memberId }
            }
            if (vesselId && vesselId > 0) {
                duesFilter.vesselID = { eq: +vesselId }
            }
            if (filter.vesselID) {
                duesFilter.vesselID = filter.vesselID
            }
            if (filter.trainingTypes) {
                duesFilter.trainingTypeID = {
                    eq: filter.trainingTypes.id.contains,
                }
            }
            if (filter.members) {
                duesFilter.memberID = { eq: filter.members.id.contains }
            }
            if (filter.date) {
                duesFilter.dueDate = filter.date
            } else {
                duesFilter.dueDate = { ne: null }
            }

            await queryTrainingSessionDues({
                variables: {
                    filter: duesFilter,
                },
            })
        },
        [memberId, vesselId, queryTrainingSessionDues],
    )

    // Load completed training function
    const loadTrainingList = useCallback(
        async (startPage: number = 0, searchFilter: any = {}) => {
            const completedFilter: any = {}
            if (vesselId && vesselId > 0) {
                completedFilter.vesselID = { eq: +vesselId }
            }
            if (searchFilter.vesselID) {
                completedFilter.vesselID = searchFilter.vesselID
            }

            await queryCompletedTraining({
                variables: {
                    filter: completedFilter,
                    offset: startPage * 20, // Use 20 items per page to match pageSize
                    limit: 20,
                },
            })
        },
        [vesselId, queryCompletedTraining],
    )

    // Memoize the unified data calculation to prevent unnecessary recalculations
    const unifiedData = useMemo(() => {
        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Use unified training filters hook with client-side filtering
    const { handleFilterChange, filteredData } = useUnifiedTrainingFilters({
        initialFilter: {},
        unifiedData,
    })

    // Simplified data loading without server-side filtering
    const loadData = useCallback(async () => {
        setIsLoading(true)

        // Load training session dues without filters (client-side filtering will handle this)
        const duesFilter: any = {}
        if (vesselId && vesselId > 0) {
            duesFilter.vesselID = { eq: +vesselId }
        }
        if (memberId && +memberId > 0) {
            duesFilter.members = { id: { contains: +memberId } }
        }
        await loadTrainingSessionDues(duesFilter)

        // Load completed training without filters (client-side filtering will handle this)
        const completedFilter: any = {}
        if (vesselId && vesselId > 0) {
            completedFilter.vesselID = { eq: +vesselId }
        }
        await loadTrainingList(0, completedFilter)

        setIsLoading(false)
    }, [vesselId, memberId, loadTrainingSessionDues, loadTrainingList])

    // Load data on component mount
    useEffect(() => {
        loadData()
    }, [loadData])

    // Check if we have any data available
    const hasData =
        trainingSessionDues.length > 0 || completedTrainingList.length > 0
    const isAnyLoading = duesLoading || completedLoading || isLoading

    // Check permissions
    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <div className="w-full space-y-6">
            {/* Filter Section */}
            <Card className="p-0 xs:p-2 md:p-auto">
                <UnifiedTrainingFilter
                    memberId={memberId}
                    onChange={handleFilterChange}
                />
            </Card>

            <div className="space-y-6">
                {/* Unified training table */}
                <div>
                    {isAnyLoading && !hasData ? (
                        // Full loading state when no data is available
                        <div className="flex items-center justify-center py-8">
                            <div className="text-center space-y-3">
                                <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                                <p className="text-sm font-medium">
                                    Loading training data...
                                </p>
                            </div>
                        </div>
                    ) : (
                        // Normal state with data
                        <UnifiedTrainingTable
                            unifiedData={filteredData}
                            getVesselWithIcon={getVesselWithIcon}
                            includeCompleted={includeCompleted}
                            memberId={memberId}
                            isVesselView={isVesselView}
                            showToolbar={false}
                            pageSize={20}
                        />
                    )}
                </div>

                {/* No data state */}
                {!isAnyLoading && unifiedData.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                        <p className="text-sm">No training data available</p>
                        <p className="text-xs mt-1">
                            Try adjusting your filters or refresh the data
                        </p>
                    </div>
                )}
            </div>
        </div>
    )
}

export default UnifiedTrainingExample
